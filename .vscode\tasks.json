{"version": "2.0.0", "tasks": [{"type": "func", "command": "host start", "problemMatcher": "$func-python-watch", "isBackground": true, "dependsOn": "pip install (functions)"}, {"label": "pip install (functions)", "type": "shell", "osx": {"command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"}, "windows": {"command": "${config:azureFunctions.pythonVenv}\\Scripts\\python -m pip install -r requirements.txt"}, "linux": {"command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"}, "problemMatcher": []}]}
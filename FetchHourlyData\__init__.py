import json
import logging
from urllib.parse import urljoin

import azure.functions as func
import brotli
import numpy as np
import pandas as pd
from oauthlib.oauth2 import BackendApplicationClient
from requests_oauthlib import OAuth2Session

TENANTS = {
    'hydroko_dev': {
        'id': 'hydroko',
        'name': 'Hydroko',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAQRSURBVHhe7ZpZaBVXHMZjtBrXCLFSA64PmiJag/pkBVeo1F1M3IprU0XEJUhxVzBabC1S9+WhdSUPJi61FB+sC/qiQsWHqlAfbKuiouiDGtDq7ztzA5Zqcs69c++dkfODj/+cf9Dk++bMcmYmx+PxeDwej8fjCZe8wk1N0YTE0Imac0WFqF9imFZyEzVUMN6C8gP6yjQcwHgHygnU1zTSTOgBYL41ZT+aaRoOYP4Tyq+ol2lkgFADwHxbShUahRqoZwvmtcePoo9NI0OEFgDmO1F+RgNMwwHMD6EcQx1NI4OEEgDmtde097QXXff855Rq9JFpZJiUA8B8MUV7vqdpWILxBmgGm4eQTppZIaUAMP8p5RfUxTQswXhDyjy0HbVUL1skHQDmh1OOIKepi/lGlGXoW9RYvWziHADGc9EUNg+gAtO0BPNNKevQGpR188IpAJmnfIl2olbq2YL5PMo2VG4aEcE6AMx/QFmMtqBm6tmCed0cacZMQ0kfdunA5Y+ZhdYiHcPWYF6/YwMaYxoRwyUA3aQ4mU+g+4LOiRo5IjUds4EPIFHTyb/oR/Q7eqlGlLA+LrkKfEP5OhhZc/r57QUD+bcFi8defrS89OJgevPRZ0h3g3WxEO1Bq1EbNSx5jCqa9L92LxjWTdoDQFrp6RKo37Xx9PrDV4q73B/Gtv6/buhdKIB96BLSStOGR2g6OkYAr0ynHjJ1DtAdYwm6MGDJuIP5pbOvP3nauDfjzeg5CoM7aATGj9qaF5k+CWraj0Vn2k+fUXbzbv4itnVz9Aylwl9I5s8HQ3uydRX4EH1fPH9iJbNBh8kg9I9+kARX0BDMXw6GbmT7MqjZcJwQblHHo4dqOqDzwzjM3wiG7mQ7AKGnSCcJ4TZVgehEVh86xn9DwzD/p+kkSRQCEN3RAc4L2qNaLereoS70EEZ7/kEwTJ6oBCD6cWXYVTTnC10ydwet/1F7U1WCeZuZUi9RCkCU3HnYfOaLl7kr2b4atP6DluJzMf80GKZO1ALQanNDwaQy3fmdMp2AGlSBysM0L6IWgNAT4vWcFGuX3pr2K9AqzL8wnRCJYgBCa4WhSGf7pWgj5tOykIpqAHpg2hVp/TAZtUdpIaoBvEkPVF1zrqhdMAyXOAQg9PapkhB0Cx0qcQlA9EdVhBDqO8Q4BSD0Km4/IeQHw9SJWwBCT5X2EoLeNaRMHAMQI9BPYcyEuAagy+NItJMQ9L4xaeIaQC16zLY1lRDiHoBmwlS0jRD08tWZuAcg5EEhfEcIeoHrxPsQgNBMmI0qCMHp/eX7EoDQE2c9TVpHCNYfXyg5K/IKN+nT1T7ByJq/kb4bHI30BagLZx9X7viDOhG5fIyhJXM1q0c9Y/R4PB6Px+PxeN5KTs5rpuXnm/vL+OwAAAAASUVORK5CYII=',
        'client_id': 'hydroko_ict',
        'client_secret': '7e3438ee-5c7c-4fed-9ce1-d8f229d5e4b1',
        'env': 'dev'
    },
    'sdea_staging': {
        'id': 'sdea',
        'name': 'SDEA',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'viveracqua_staging': {
        'id': 'viveracqua',
        'name': 'Viveracqua',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAS3SURBVHja7Zd7TJdVGMe5g/Lrh3SBLCAGKGDB0lqZUK3Imt0jIysXkZtraaCT4aVEUZRLXAVB5pJLZjK0wKKECjTkZnKRilsQFKJS89LFclY8fc+753Xvfkvq118tn+/22e99zjnPOef5vue8DBsbkUgkEolEIpFIJBKJRCKRSCQSiUQi0WWouLjYOSAHOHBsC5LBAlAEokCaRU4meIr7dV4DQYY4H9xryAng9hBD2xSQCKrB2yCS13cCS0Al2Auigf2yZXF2oAgE6nPgOQnM4fn8eI1QawzwBhdAGMfTwG/gQUDgfvC7vnH8zgR/cDtxASvA8yAC/AISwBvgvNoU523kvlyOrwBdoBPEg1TQDzzBPjACXgUbwCmwHYUqEwjcYzCgB8TwnOvBr8p8a09BjToF/LwStAMvLtAHtIAk7lcbPQCmc/9VhnmUAWeAI5gMzoEHgB34inNPqNMGloNjwGzIV3nz2LggQ3s4mz7rUgbwGr0ghddwtsaAhWCYN9DBb/R6gwFLQTcfz0GwyGDAEBjg4iP4DaSBCt7IlWA2OA1M4CSfnjfBrr/Yy2pw1KLNnvMWTWDAbXxS3NjYedYYYAY/gMVcgJeFAR7cHsPH2M1ggLoqYVxoBL+9PPCdess8/xaO3+FCikEWnyxbi728yGMn6W2PrEpVBryr1kKx4xYG9LMB2eB7XkMZX2btNSjjxP0cXzSA4yowBso51g1Q3xBXPvLGKxALRtmY4/xhfZLv9VlwB39r1nKuDxsVwuvsAO42mQ2OoBao8Xb8xvOBE7gbXFCG8DdjM6+xmtcwWWPAfVzQs5cwYD7HD1kYoPOj6jMY4MLXpQ78pD56hnt+iv+KzGdzVP44f4vU6buFP5AUvWKNP4o/Dc6BSSj2LnCcr4IqPh3j5oKf1cnkNZzZxKf/WfVZh0wOmQe9A5J3B7tk1Pkg9nLMPOCDeIZTRr0Wq3Zj7Myxjn9yebA5vcbXD7+2WQ1easzUlKoA1ee7qSJIxTo3bNoT6JH6nr96Nr1e6xuWWBAemlR6s56nUPu5fV3R7OtSKj1R+HKQhHY31WdOr/UNTyy8M2jjrptUfHVatZ/lGj6b9+pruP9d8cHgLBj/j6L2tg6sAif/Rf55MHciA6IAKeI/HaIn9vXQjaXttL75W3IvaKHiL8bIKbeRMo4cI89trRRbP0gJGOeypYlqh8/QNYWt9NwHfaTPoZ5Nec1UcPQE3fpWJy35ZJCW1g1ScEkbzdrZSS/U9NPDld10bVErRez5XMuZWnSYCjF+enEbrTk0rOUZ55yytYWSW0YotKxD65+5s4MWfzRAKYdHtP7SL8doMvaT3DqizRG9v1/bv0NOoz5HwkQGROoLvYzNxtZ/TYGYZGXDMLmikJz2UbLLbtSMUWNiUMBLHw+Qc26Ttmllit6nUM+ueU2U1TZKM0ra6ZnqPlr4YR9N23GEQsraKer9XnqsqlszLry8S8vxwBxqvPf2z+gVmKWMWlDdS2G7uy7OuwEF2WMfykxlwONVPbS28RutLxt7NOc306MwVhUdiT3EHxzSxnN+/EQGmEEJqPmfUgG85Z8ekUgkEolEIpFIJBKJRCKRSCQSiUSiy1F/At7ozabNUWZ5AAAAAElFTkSuQmCC',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'vizmuvek_staging': {
        'id': 'vizmuvek',
        'name': 'Vizmuvek',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxMAAAsTAQCanBgAAAUfSURBVHja7Zh9TFZVHMcPIFCICAwjmmE1K1+i0lpM1tLouU/50kZpVBilRFnacurW4N4ndKYjGwU64nkee51bmc2py0yl2Ru2WeZ7Ac+VZe8vc9qfrrakz7n3N310STNYBJzv9tnv3HPPOff+fuflnnOVMjIyMjIyMjIyMjIyMjIyMjIyMvpHWU4CDBrAAbDTYeRAHgGjYfJADsBKeHegOj8ejsIJuH2gzf0bcXovfCe45AXPq41ILE1F3CewZV2Xcy+Gef+nnrdgLaw7i7eg7BxOTMTReT7uLBU5nIp9GKLwiAq3JWDnwINwpZS7RYXdDNLPQmVc4Erl/uMwXtrPhUXwlIrGRkneBKiCJ2E4da6QeoXd7f3BOPorVs//b+E9eAVOqoCde44A3ABfQjHUwl2wCQK+jU3GroBGFY5pZ76GAtIXYHdBq4q2Z0oAHpW6U+CgirTqYG4lvwI7A3arppjOO0TerdiFUEpbWVgXru5uAPS3vx5n75RFcD7pUuwaZYWS/7ZO2M3jwT9CHS+V7Q1p3WN+cHTPzaXMSGwLVgdrs9TLJ70RVkOFlNeO74DHPGcjbalecE8H+3OcHYJtkHIvwWi5tx8yujsFcuBj+O0sDsFVXQTgAM6PU9G2FG/YR9waeanFUCnpFpkWs+V6PmyBNdCswu0JEoBmeB6Wq6bDOm+virSnY1M9J8Pe+jKd8oOxDiztuQCc/gL8CZ1xTO9iIZvp9UzELfEDEtMB2QYfyPC9SMrp4b2H61x/zrqfQDnpgB9Ab4gzitwN5DGv3c/gOngIdkoAq6Qt3fPrYDtli2Ua7vGmQ7cVtC/F4Y/gGzgGG6Fo4HwG/QBEYIfgnHcArFAKdWb8i9FX5I22gD2k7wTAci73X9zOxhbC9XAHzCEvT5wqknvZkh4mdYfL9SXSxj3e59ZybpKt+AQvGH6ZArg2rr3LYKyXDuhzC2UtJ783AhCEn3j5EdhmqCW9EPs+tgL7IjTAPnnpDlilbgvpL85mWWAL/A2XMwkekOfO8vL059dyvoAFsAK2+u9k12Cr/Gfa+dJuYe9MAb1zDNi6F3dCHulibCMvxlQIZZJu8UaGX1YHaTuM8c4alrNF8j+EDOqMw76sAk4q9iDXuv4BZVWnyejQgdWfaT3Npsh69Tbc11O7wXJvL2A5G4QFXqS7rrMElsE7vHjCqQAEn07Evi7TQX9dksV52w+KPTMuAPrzmyXlonEHshC8KddlsjEbwzPyJAB6p7rYo4cCcD/oIbwaNvFC/nXXdQr8HaRTIdc10ut3wy/iyC7augbbhp2N3S33W2UNeEZ6Uk+Le2VjNtE/lNkl0m6jjDIdhBdgOXxKMEb507AnRoEfcT2sfoA/YL33wK40aQm9Xs0myvZ3i4HqoXJ9IXaYkOMNW53WwzlQnQ5p3nXQGcTISZRFMTduZ5rk369OknazTrVnsTj6z+G6Klny07vnfCCU5vf6GZugTm8BC/bmp+m/Ow02xDn9vQqG3lDl4VXK3lavGvbXyU5tqbd3j7hj1Wv7Evqus00dySp85EwHSmrnqspX69Sylnq2mOtx8ih0CsfgpKT1tvM5mMZxt4/+OA3HUuSo2uCfvNyf4fc4JzXH5cSmT14noIM6Uzn0JPWfYa9/WITdoXCz/MhYJMfRaXJiOyJBWYvzOf17DYi267O2BfXi+HHp/akcPxP7/yIY/SoRZ/VPDf2zYgRkKiMjIyMjIyMjIyMjIyMjIyMjI6O+pr8Aj6AskQqboFcAAAAASUVORK5CYII=',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'promedio_staging': {
        'id': 'promedio',
        'name': 'Promedio',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'cile_staging': {
        'id': 'cile',
        'name': 'Cile',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHIUExURQAAAABSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkgBSkjBU6pcAAACXdFJOUwABAgMEBQYHCAsNDg8QERITFBUXGRobHR4fICEiJCUmJygqKy4wNTc+QEJDRUdJSktMTVFTVVhaXF5fYWJjZGZoam5xc3R2eHl6fX5/goOEhYaIipCRk5WWl5ucnaChoqSlp6ipq6ytr7CxsrW3uru9vr/AwcLEy8zQ0dXW19vd3+Dh4uPk5ujq6+zt8fP29/j5+vv8/f6A7C4QAAACgklEQVRYw+3X21vTMBQA8Kzt3HSITjcVRB1TvA68oKJjIijqRKcg3sW7jum8MJl3BScOVNTZbu35d7Vp576maZv4xAPnaUu+8/vSJG1OEFqMBR3LNiT6DyN0fKBzU4g/O5zKTv0CKCDfF4DKdO5ohCfbh9B5wDGBfJ+NX+O4mS12ja9E50ggh5rye5jSo3fl2WYaECor2RbPdKGrBDC7gg4AlLsF93xx8O/M6YA5BwUCAHlIdAVGVTCAjkM4EiQA6hWXMQijGphAY0UIALRrjmMQBmtQB8IxHGvtAKhnnMbQVYF/QOY3jmcUAOT99PzIJ2gADqtg9rdSgdvACkCelr9DYQeq+ygzWAR2AN7YV2I78ACw2/YC3ucDnpBDiPzkA+T1BJACPgCOEcADXuCpNX/pFDtQk/UohS1ArL6L1ZmS5gFcXqNHVLIAO838bEcgsPnOTLMbcIG2DXuNvutL9D9SepUb8O4GDutOOIW75pYb//yi9yTCkAXI4LaxRoM3kKEAI/8PpHHbQx7A+gh9uO2HuT+bgm7A+3s4ui1AwuibxLMYGgtzL2NcNjo/9MU2Jt+6b6SrrXq0+K1H+bT5ZJqieO1EVdGjvNr6OXjE+zJNEGf1AC9wkpiEdRU+oNpOflMf8wEFiVyHTj7ggP1gf80DfPTbt8LeGjug9tAqqzw78IJ6wrfNsQLzcfrxfFBmA6p9TgXSsMYEXJScShTpJguQCzgXSdItzRPIBd2qNP+I4g5ULwU9CsXkVzdgvl/yrFXbn9ecALW4haVYFnsm6cX2y6SfsVwXBTRsBwSJ58rQdrr4Ta1fONTvr87Gue8sQmRrKt2L0In0kW1RcfEGuKDjDyPEq60TrXAoAAAAAElFTkSuQmCC',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'wmd_staging': {
        'id': 'wmd',
        'name': 'WMD',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'oasen_staging': {
        'id': 'oasen',
        'name': 'Oasen',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsIAAA7CARUoSoAAAAYUaVRYdFhNTDpjb20uYWRvYmUueG1wAAAAAAA8P3hwYWNrZXQgYmVnaW49Iu+7vyIgaWQ9Ilc1TTBNcENlaGlIenJlU3pOVGN6a2M5ZCI/Pg0KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgOS4xLWMwMDEgNzkuMTQ2Mjg5OSwgMjAyMy8wNi8yNS0yMDowMTo1NSAgICAgICAgIj4NCiAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4NCiAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjUuMCAoV2luZG93cykiIHhtcDpDcmVhdGVEYXRlPSIyMDIzLTA5LTIyVDIyOjM5OjE4KzAyOjAwIiB4bXA6TW9kaWZ5RGF0ZT0iMjAyMy0wOS0yMlQyMjozOTo0NyswMjowMCIgeG1wOk1ldGFkYXRhRGF0ZT0iMjAyMy0wOS0yMlQyMjozOTo0NyswMjowMCIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciIHBob3Rvc2hvcDpDb2xvck1vZGU9IjMiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MWY5YTQwYmEtYjA0Ni0wMjQ3LWIxZGMtMDMyNjMyNGIyYzA4IiB4bXBNTTpEb2N1bWVudElEPSJhZG9iZTpkb2NpZDpwaG90b3Nob3A6NGJkMTY0YmMtYzc4MC01OTQ3LTg4ZDQtZWY0MmYzOGI0ZjI4IiB4bXBNTTpPcmlnaW5hbERvY3VtZW50SUQ9InhtcC5kaWQ6NzFlNmNlNmQtYWY5Mi0zNTRhLThjYWUtYmMyN2IyMDhjMTllIj4NCiAgICAgIDx4bXBNTTpIaXN0b3J5Pg0KICAgICAgICA8cmRmOlNlcT4NCiAgICAgICAgICA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0iY3JlYXRlZCIgc3RFdnQ6aW5zdGFuY2VJRD0ieG1wLmlpZDo3MWU2Y2U2ZC1hZjkyLTM1NGEtOGNhZS1iYzI3YjIwOGMxOWUiIHN0RXZ0OndoZW49IjIwMjMtMDktMjJUMjI6Mzk6MTgrMDI6MDAiIHN0RXZ0OnNvZnR3YXJlQWdlbnQ9IkFkb2JlIFBob3Rvc2hvcCAyNS4wIChXaW5kb3dzKSIgLz4NCiAgICAgICAgICA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0ic2F2ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6MWY5YTQwYmEtYjA0Ni0wMjQ3LWIxZGMtMDMyNjMyNGIyYzA4IiBzdEV2dDp3aGVuPSIyMDIzLTA5LTIyVDIyOjM5OjQ3KzAyOjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgMjUuMCAoV2luZG93cykiIHN0RXZ0OmNoYW5nZWQ9Ii8iIC8+DQogICAgICAgIDwvcmRmOlNlcT4NCiAgICAgIDwveG1wTU06SGlzdG9yeT4NCiAgICA8L3JkZjpEZXNjcmlwdGlvbj4NCiAgPC9yZGY6UkRGPg0KPC94OnhtcG1ldGE+DQo8P3hwYWNrZXQgZW5kPSJyIj8+sxj4SgAACdtJREFUeNrtVwtwVNUZJiBoQUEqBU0tImAtIlQBoUIREMnu3uduEhDRmgFG7dM+tDqOLXVGB6t2bLVqRSja0lIwJPc87gJiSpkCAUFbFZApBtjd+9zdhLQ8fAB6+52bJQ1jlpm2M4zT3m/mm3tz9z/n/P93/v8/Jz16RIgQIUKECBEiRIgQIUKECBEiRIgQIcL/MySev1gzC19R0/5UlftXa9yfqXLvSplZfbqzT3C3r2q6YzXuxjXuVONdUUx/gkTdAeXWSFJ3iEzsKTLJ6RKYoM60GC0MP5NfMveGK6YnfKlWeF6WeesYrb7lnO5s1TXZXjHDGgFOjRuWnGjMJZSGzCSVW5VlF6imucs06v9BpflDGi8EOi+kdeov1KgVaMQ6qRFnl2a480/ZX7M+OEdpcO6TiP2uwtyPVOYGgjLvoEKdHMbcH1sRVJwaoxlvV8rUWg67NvwubAKpRJm6x3Rq//lmlr3hNIEbstNg96rK/GMqy2MM5maO4EmFWjtSzLmpq73CbEUhzk7JcN6XCXwJ57YwDmR2u8IsWtNoXXFa8OPZ3y+QifdXmWcDhbtHNZZfmiL20B8sfrxCo9l7IEBGIzaEcOFsoS4MptF5QF+DyQ1bBNKOoHaCTQh+I/iuEEOjQgz7EWF/fdo9P8Gzr8vUhlCeEGyPypxmrLe9w94LIECgm1ZBZYfCnYrRzC0SyZxUxRjqf4SN+RvGvaYx+22VWcdFcEnS+kE186eGWWLsn4xAPxTCyoZzRCHe6yp1NuLbJnC3Tq2PERditHfF12Yu7BRAY35KZj6cdT7UTG/GJ8qCehfCuY262C3uNYeLEet3CoJXDWsRFqmsq9/TszPFzWxvnbr3C3uNZ47UMGuIRh0tQf1AYl5e4/nJt67a26tz19Ju3yQpTNAbixtlVjyup4sjalcXBqca7KJMswg+t0Wh/njdbD3vDrqzYkHDnl4QYVKMWbZCPQjtrwrXJdl6IRYE2K3S7MiuMYxmbT1raFZJMfs9KY0M4tadPbo4/JDYAai8W23c06u7ElGM7Nc0kkMauX6cHB1YxbzKKuKo5Uoqxf1JGvOOqzwXpKidQErWJXgGAlgtMZq/tLsxc35vfTZp2JNLfWJByvCDKlZsj/HMsG77j+lsUcJM89ZPfWV/L5V6eztK0XuwnF8qtTZLPMyCZV0ajP8CFBYCbCrbHFkmJlJOYd4xmbmdNaQ2eKM04t2GzFgkp/1nJNN9WTX9ZgR/OIms0jh2iHl3xeiBUQmW+Rh9IdAMpw3PzcigleAv0BDvia+zElNezV7QKaBZ+KUYnzC9DT02e+dKpjdaItZMhdhz1LR3L5ptEwh/Qr/vTpBcP8VwfTnsEV5duTg0aq8WZYAM4l06v//bkgDryg1E/c4Q6QZ1P5C5deW8tYUL0A9WJRvc45rhifIJFOGQiUZlhjW+CwG0I90DlefvCedgzt264R7ShQhhT3EClYhmKMaDppWZuc5WwgxghZU6KwRS2rfRN/ZpRmugkDwyyA1SoM5EedmH4c8Twj5O7CFxareJTdIM65ayAjD3xRQJ+9OGzo+o76VQLUAf+FO5gTrPSaWBx1DHl2OiZZqoP+KckJhTn6QHv5MkLTU6bZ0eT9sjZ9HCIDiXESeKyls7T4/ahrZLktyrUXj2QXTlZ7Hmmlnp/A6V2cdmGy3YGcfT1rv9sRm/1qgQDycSdfIKLaKRFVbCz6eT1H9AIe5sjeaGnpo3Rr1L8K1VZmFfKisAMma5TnzM6a7v/FhDrIfVDgH2zDRbuu0BmmkvTKKGZZrzlUZ3OBrfAVXsHCs+323JGG4VUj0QmSXx4nXhN7J/2uWvvNSzO/s42ztRM3JHNCLS3h2H4/jHECFIMi+bIO2Dy/Yamq+Ombm+CdY2RCV+hwCN9i1nuOcsF8JqXQVQuJMSi+GsPalyt/YTl5A17kUSyzaJYxKlsCW1yuuDTrtL1B92plldu3vYDSyoWFQXVOj1dj+13o3pxNuvoh5jZn5Hh/I50QgDxfBfTPLCF29bfbTzYjV/yXu9FWLNhvMn4tz/+Eaz7QqZH5yBOoUA+fd15qW6+lO74livhNE6Ujfalok14jyHHnB0gErybR0CWGcQwFkujufTBUgfPk8n7jYQ52rxo1Rj/i8KzaUlZjNcfjaqjY4rkyx2Wwhgzenops5dEA4pi4sSP/gPNLo3cE94DTe7AzFxFKHGVWIXq9L+xI6jNHu9iq6uG/mghngf4Jb5DhrnVly0mvUGb58qzufwIpVf3Zl1xCLhUYpLDI7DtxI026Qa2SaJeG/KNPMeygY+OUHctBKycXiAQgrtkrgkNTplBZBNZ7mYE1x/epNr2DUMXZaiyRwRNy0EGKhhRw1vdQGOpxadHLjr9IZi35EkfovOctipcNLQ9ibutMdZblU12f+l09K1oTgXJfFWNXGO63AeF5Pw8gORMS7noBE+luLv9+ssC6PYX2YHn1NYpk0ORciFtzoJ60n84HGU7Jtx0731XyeVu0IWjXqNN+8MTTCFjTuRIrlXujWIp/2h+trC9Bru19Zy7+ZUOi+ra4vXVJnt/bqzr6XZ8+Pr3InV3EmmuFUtpe3pqOHPl3PgqxsyvWEzSmXZWbhXVKNP6LLhTZpFigPLjYlz+2I0vKm46KRSJFuD96oYd0aNayr27mo3t2EfrufZa7XVucFn+r9ixtqWYQrJjYr+8/tvsW3btp7guf+G/bnNzc09/5cE+Bz4VNdvW7du/QKCvLuM/aP4fcx/sM4UzHnVpybwTZs2fQaBfAtOPQnnGsELQQW8FZTAm8GB4BzwfvFdjIP9cryPAUeAccwRA0VWTAIrweHgl/FtGrgIrMNavWG7AqzHb9NBsdb3we/ivS9sxoIquBAccFYE2Lx58yNY7KdwYgGceBnP0eA7wqmSCL8CrwHfAJOwaRI7CD4NzgNXbt++fTTmWAJOBsXv94I/hP1s/D0RnA9uAOfi+8/Ap/B+NZ4vwOZ74GLwIfx9X2n++eCAs7H7FVjIBC9CEGKXf1MKdmkpXa8ChZPjwGdLO/8keAP4HNgO3l4ql9vBZeDP8e158CVwGPgg+ATYCC4Ev71ly5bq0vw7wYdBsQF3gg+A885qCQhH4fQCsdt4ZyUBVpQcFO8vghPEs2T/DDhN7B74dZBDvPGY4zK8H4LdZDyFCH8E+zd34NrSuG+Ad8L2MYhwKd7rS6U1FhQ2PwEXnFUB4MwILLoEXAzWwZFKcG5JgMqSg5eKZ0mAZKm+azB2UMl5EVgfPL+J53l4jsezumQ/rzT/j0R/gFiDSiUwD+9DYfu4yC5Q9IQbwes+tScFnKuILgwRIkSIECFChAgRIkSIECFChAgRIkSI8CnBPwHdvJ6lQXlu4AAAAABJRU5ErkJggg==',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'swde_staging': {
        'id': 'swde',
        'name': 'SWDE',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': 'b06a78a5-0d31-46b6-9259-ae6cda55ee84',
        'env': 'staging'
    },
    'dewatergroep' : {
        'id': 'dewatergroep',
        'name': 'De Watergroep',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': '33b95330-8977-405c-bc94-f2e328868fa7',
        'env': 'prod'
    },
    'iwva' : {
        'id': 'iwva',
        'name': 'Aquaduin',
        'icon': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAj5SURBVHja7Vh7UFNXGkfEF/hA61TXdn20Y52ydkdBQORdFBS1IQQSQkJ4hkfePBJDSCCEtwLKM7n35kV4CFSRPIigFmetWme12m53OrPWdjvbdWer/cN229U6avY7aFrX2XWc6ewMM3t+M3fuued75Hy/8/vOvRMfHwwMDAwMDAwMDAwMDAwMDAwMDAwMDAyM2Yqu91oXdpxplvROt0U/z6/jdMuyWpeittmtSX7h5Dmy/g2znYCGiaqXquySBzWO8kPP89M65YmqcbFH4yj98IUS0wos7Ih04rs9PCrieX7l1cdXi1Ujr6JxD3VyQeNh+wKvbXzy2rwy9btrWzrc/ui56bB98RFyyre1x7mwtXvC3+vX1uP2PdjhXNzWPeHnnatuGV8qVA5vsI1emO/xPJjTdHg8oOPY0ByvvedM6xrjua6Xes+0La0al3xX4yhrRPPDH5gDjv1+wNfrN3LRumji2tj8FncNUkBzvUu5H80PnTcvRveB85Q/dbZr5bM7vwmK/zaYbngYzjB8xhXaAp8tXKIeWb6TaxyKZJJ3wfdHGJ9I4FCXE7OMB5GdUWRJiGVTn0amk55oFvnFnmxjT1wGeSO3bHDdTg7VDv7nhuwfzhScXmx5I45NfkXLt9DR855sU0UUi7y9I514GMemriZlGY8nZFLnmxO/9ms/pV0Du+1W26X3NQ7Z9zqn4miVXfpDjbO8usGl2lHtKLvZeFId7F0n+Dq1LrnRMN2+pNpReq3OpcwBIraA/436icp2yPEXtUP2Q61Tbur7HbFoJgh+9OzWFMPHUTTyzRC64XYUkzQ8S0B8JmUEgjzb6PoDISm9u0JTDa6t8BydQenFquGV29OIbyHHn8JSDXywtYakgi9cHFH/60DYYEQ6+eXgiUt+j9Vm3oxsO7NM6ftyTEloHELXDwbT9QmQvx79TkQa+Wdry+fzoQgnkrLaLquEYnOh+M/Qc61LXlXnUCapxiUenVu5w7vOqnHpx1Cgu+O9huUquxi1iqLaWRb2OIf0JuTgQ6529FznOnDAh8a3vIMWEEmjtqMEETQyHQp4mJxjesubVKA8GhCeRtzaRjeYvXOMAtMKiPs+hk11JmaZ2KjgeDa1xWuPSCMoyOPhAgE7mGQ/kHB9bOrqXGRL4ZuDQhkGTxLPTAeyCfD7u0Rhme+NBRW6wlPJL5vHjqyHfr+rsZe2em31E8oIVDTsoLrOWZkIdk+dW7nda4cir0DfOzqnm5ZX2cX3ahwVFdXO8jAgw6NzHWD/RJRd8gmoYdoHZOsIeYeYrJ+sUMPELa29gr91P/HHSCbR4nXmia2LwxiGb2CX9d45ufb4knAGcQcU0AmFZyES6QXmTV47qKr7iQJeA2mbgZC/fnPrzkyv0vjmMGQDAlKAGBPk/eqjq9d/6uNIFjGGCGg81r7hcRHldV5bi7v6LVS01lmhrnUo4tC4wV0Z+zMBsuugAHvndOMzBEg80AJxTxHwPpB1EXaK/Cgug6K0J0XnK8dFnpoJUX84jZqCXRh9ugVAAWOw6EegDE526VBQAtdoQVKNyaC6k3nUemidRzuYxAVmsS1pf565FHb4R/B/wCqxvRbFNBQihcB5oQVCYqIzyAsoNnFGAQYWxHoSOMa23NKhN/fmmgWIHGiZz3sOXlmgtktOw2LvNbqruK2TujhoiQszLeCUaw+6a1bBGMn8bPup+qhap6Llycl//AkB94EA+UwLIKVMVCY8RcBFIOADn8h0ondbCvF1KdkkqHbKekpaj0hD6cT9cIZe8jQBb7OJ9dCfl9DigAwP7No5uG4AAV0zkmcY8qCQO0jaqDVg16/A+EEq3xqUV6r3AwUdQ7FhDOKf29MMIyD727t5ZiaKhXNDj0hAecH+BahlGg7bTw6t/sfchpOK12Gxl+Dk96Dd1thl07Dwv8FrToNiNY4yCbTEXTgb0DkBRcmuAgHDXWeaAsH/ltYhl9Q4K4LBfq9hQvWUUqRTcJ3y2ZtN/goW84cQGnF3e4rxUyj+UWiqfoqWSy169iBMz+n1g54NjmZSoTkC0m8Xx7gyiWtc4bXHsnpXxrKI0HgWuXInl+IiMlIKLJt/tpNvgH3dY8L0a5J5pgCvLYZJboxlUdE72cRyWrZxUSKHXFNHDMy8BvtPG+bCTv5G6yjfPPb+oG+dU7GqyaVa5o2FVoA3RUWQZcrgp3MqA0EJq4zuLl+do+LVZpdmCfjOh5i1hya1C70xdQ7Fy+iaedjN7YV+1ueFpOib4c5k5BLzfukHC7z2ChABdL7lt/+Xn6z7ck2hMSyinVVsXf0i/hxhX2C21LZWXjuyqFA+tBG+O9YIVSO/Ptg5tUBRd2Jhc/ekf4ni6Koyzegrh/ST/gfqxpZUtziWKnTHAnTtzsWqRntgdfN4gHP6sq9p6Ow8Zf3xQGX92Csyzbsb5NoT/k2dEwt1rS7/WUvY3hxqHS3PVM8V2ZoZhZaOjJK+8lS+pZ0tsOl4koEWnqQ/iy2wbkkvsh6Bg1TAKumTsIV9GiC4LEPQpwI/eUaJVQVzOragTw3xiixJfyPcVVni/sM8cb+uWDG8ZdYSAApYlpJvjs8U9O3PFFpjuaK+t6HYhPQiCx2UkVxQPrSOWWxZwSyyJoFPaDLPGALFMVILTIlABj1LbAsCMkJpeebdQGQEg2+mZQptUaxicwwQk5YrHYgsrBjYVFEzumzWkjA0ftmvts21/H+V/+btO3NmbfEg05fTCi1lXJBqiXI4Lr98kFMkH9orUo1KJOrRImiNQo7IJgL550E7CEHeImaxVQlq2Af3jRyhTZIpsgmhXdQ5soHCHOkAH2KKoD14POlgaaawLwsUIQZfHoxRewhBQexZQwDIfxV8SZbAovJB+sWMQjOHVWwphkXHw7dEDsicy+BbhQy+JRfsYvjEpkOv78oUWCPzZAPhQIQecpRnSweKgUgetEIR5GMyCsz5kDMeWieNXdIXBbH5EFfKE9v2QsvEzSoVcAu6/7NEg4iZeXGlfube3u36Nz844ALSCs3BMrXVF/9lhIGBgYGBgYGBgYGBgYGBgYGBgYGBgfFf8S8mqVMHk8q37gAAAABJRU5ErkJggg==',
        'client_id': 'hydroko_ict',
        'client_secret': '33b95330-8977-405c-bc94-f2e328868fa7',
        'env': 'prod'
    },
    'robertec' : {
        'id': 'robertec',
        'name': 'ROBERTEC',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': '33b95330-8977-405c-bc94-f2e328868fa7',
        'env': 'prod'
    },
    'waterlink' : {
        'id': 'waterlink',
        'name': 'water-link',
        'icon': 'data:image/png;base64,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',
        'client_id': 'hydroko_ict',
        'client_secret': '33b95330-8977-405c-bc94-f2e328868fa7',
        'env': 'prod'
    }
}

ENV = {
    'dev': {
        'HOST': 'https://hydrosense-api.cmas-systems.com',
        'IAAS_HOST': 'https://hydrosense-iaas-api.cmas-systems.com',
        'AUTH_HOST': 'https://auth-hydrosense.cmas-systems.com'
    },
    'staging': {
        'HOST': 'https://api.hydroko4iot-staging.com',
        'IAAS_HOST': 'https://iaas-api.hydroko4iot-staging.com',
        'AUTH_HOST': 'https://auth.hydroko4iot-staging.com'
    },
    'prod': {
        'HOST': 'https://api.hydroko4iot.com',
        'IAAS_HOST': 'https://iaas-api.hydroko4iot.com',
        'AUTH_HOST': 'https://auth.hydroko4iot.com'
    }
}

MAX_PAGE_SIZE = 50000

TIMESTAMP_MAX_UTC = pd.Timestamp.max.tz_localize('utc')

ALARM_TYPES = [
    'BURST', 'DRY', 'LEAK', 'LOGICAL_TAMPER', 'LOW_BATTERY', 'LOW_TEMP', 'RESET', 'REVERSE', 'RX_FAILURE', 'SESSION_COUNTER', 'TAMPER',
    'ENCRYPTION_REPLAY', 'WMBUS_DATA_FAILED', 'FORWARDER_WMBUS_MIDNIGHT_RX_FAILED', 'GPS_LOCATION_CHANGED', 'METER_INDEX_TOO_LOW',
    'METER_INDEX_TOO_HIGH', 'FLOW_ABOVE_Q4', 'METER_TAMPER', 'HW_FAILURE', 'NO_CONSUMPTION', 'REVERSE_FLOW_VOLUME_INCREASED', 'VALVE_ERROR',
    'WMBUS_SESSION_COUNTER_TAMPER', 'CONNECTIVITY_LOSS', 'DOWNLINK_NOT_RECEIVED', 'DOWNLINK_FAILURE', 'VALVE_STATUS_ERROR', 'MOTOR_DRIVER_ERROR',
    'INVALID_PRESSURE_READING', 'WMBUS_SUSPICIOUS_COUNTER_TAMPER', 'DEVICE_LOW_BATTERY', 'METER_LOW_BATTERY', 'DEVICE_LOW_AMBIENT_TEMPERATURE',
    'WATER_TEMPERATURE_HIGH', 'TAMPER_SWITCH_TRIGGERED', 'METER_LOW_AMBIENT_TEMPERATURE', 'ACOUSTIC_SIGNAL_HIGH', 'PRESSURE_OUT_OF_RANGE',
    'DEVICE_ABNORMAL_AMBIENT_TEMPERATURE', 'HOURLY_INDEX_TOO_HIGH', 'HOURLY_INDEX_TOO_LOW'
]


class ApiClient:
    """
    Class for accessing the smart alarm API.
    """
    def __init__(self, customer, domain, client_id, client_secret, env='dev'):
        self.env = ENV[env]
        self.customer = customer
        self.domain = domain
        self.client_id = client_id
        self.client_secret = client_secret
        self.token_url = urljoin(self.env['AUTH_HOST'], f'/auth/realms/{self.domain}/protocol/openid-connect/token')
        # Create a session with auto-refresh and authenticate directly
        self.client = BackendApplicationClient(client_id=self.client_id)
        self.session = OAuth2Session(
            client=self.client,
            auto_refresh_url=self.token_url,
            auto_refresh_kwargs={'client_id': self.client_id, 'client_secret': self.client_secret},
            token_updater=lambda token: None)
        self.session.fetch_token(
            token_url=self.token_url,
            client_id=self.client_id,
            client_secret=self.client_secret)

    def _get_page(self, url, params, page_number=0, headers=None):
        if headers is None:
            headers = {}
        r = self.session.get(
            url=url,
            params={'page': page_number, 'size': MAX_PAGE_SIZE, **params},
            headers={'x-tenant-id': self.customer, 'x-admin-id': 'master', **headers}
        )
        if 200 <= r.status_code < 300:
            return r.json()
        else:
            raise Exception(f'{r.status_code}: {r.text}')

    def _get_all(self, url, params, prop_name, headers=None):
        if params is None:
            params = {}
        first_page = self._get_page(url, params, headers=headers)
        num_pages = first_page['page']['totalPages']
        if num_pages == 0:
             return []
        results = [*first_page['_embedded'][prop_name]]
        if num_pages == 1:
            return results
        for i in range(1, num_pages):
            results.extend(self._get_page(url, params, i, headers=headers)['_embedded'][prop_name])
        return results


    def get_alarms(self, params=None):
        return self._get_all(
            url=urljoin(self.env['HOST'], '/alarms'),
            params=params,
            prop_name='alarms'
        )

    def get_hourly_data(self, params=None):
        return self._get_all(
            url=urljoin(self.env['HOST'], '/data/hourly'),
            params=params,
            prop_name='hourlies'
        )

    def get_maxflow_data(self, params=None):
        return self._get_all(
            url=urljoin(self.env['HOST'], '/data/flowValues'),
            params=params,
            prop_name='flowValues'
        )

    def get_pressure_data(self, params=None):
        return self._get_all(
            url=urljoin(self.env['HOST'], '/data/sensor'),
            params=params,
            prop_name='sensor'
        )

    def get_ald_hourly_data(self, params=None):
        return self._get_all(
            url=urljoin(self.env['HOST'], '/data/ald/hourly'),
            params=params,
            prop_name='aldHourly'
        )


def add_tzinfo(series):
    if series.dt.tz is None:
        return series.dt.tz_localize('UTC')
    return series


def main(req: func.HttpRequest) -> func.HttpResponse:
    # Auth requests return the list of available tenants
    if req.params.get('auth'):
        return func.HttpResponse(
            brotli.compress(json.dumps({ k: { 'name': v['name'], 'icon': v['icon'] } for k, v in TENANTS.items() }).encode('utf-8')),
            headers={
                'Content-Encoding': 'br',
                'Content-Type': 'application/json'
            })

    # Read query parameters
    tenant = req.params.get('tenant')
    device_number = req.params.get('device_number')
    if not tenant or not device_number:
        try:
            req_body = req.get_json()
        except ValueError:
            pass
        else:
            if not tenant:
                tenant = req_body.get('tenant')
            if not device_number:
                device_number = req_body.get('device_number')
    if not tenant or not device_number:
        return func.HttpResponse(
             f'The "{"tenant" if not tenant else "device_number"}" query parameter is missing.',
             status_code=400
        )

    # Make sure the tenant is valid
    if tenant not in TENANTS:
        return func.HttpResponse(
             'Invalid tenant.',
             status_code=400
        )

    # Log request parameters
    logging.info(f'tenant={tenant}&device_number={device_number}')

    # Create API client
    credentials = TENANTS[tenant]
    api = ApiClient(credentials['id'], 'master', credentials['client_id'], credentials['client_secret'], env=credentials['env'])

    # Fetch alarm data
    alarms = pd.DataFrame.from_records(api.get_alarms(params={'device_number': device_number}))
    if len(alarms) == 0:
        alarms = pd.DataFrame(columns=['id', 'alarm_type', 'start_time', 'end_time'])
    if 'end_time' not in alarms:
        alarms['end_time'] = np.nan

    # Drop unused columns
    alarms.drop(alarms.columns.difference(['id', 'alarm_type', 'start_time', 'end_time']), axis=1, inplace=True)
    # Format alarm data
    alarms['start_time'] = pd.to_datetime(alarms['start_time'], utc=True)
    alarms['end_time'] = pd.to_datetime(alarms['end_time'], utc=True)
    # Sort alarms in chronological order
    alarms.sort_values('start_time', ascending=False, inplace=True)
    # Replace open alarm end times (null) with max value
    alarms.loc[alarms['end_time'].isnull(), 'end_time'] = TIMESTAMP_MAX_UTC

    # Fetch hourly data
    hourly_data = pd.DataFrame.from_records(api.get_hourly_data(params={'device_number': device_number}))
    if len(hourly_data) == 0:
        return func.HttpResponse(
             'No data found.',
             status_code=404
        )
    maxflow_data = pd.DataFrame.from_records(api.get_maxflow_data(params={'device_number': device_number}))
    if len(maxflow_data) == 0:
        maxflow_data = pd.DataFrame(columns=['timestamp', 'max_flow_value', 'max_flow_valid'])
    pressure_data = pd.DataFrame.from_records(api.get_pressure_data(params={'device_number': device_number}))
    if len(pressure_data) == 0:
        pressure_data = pd.DataFrame(columns=['reading_time', 'value', 'valid'])
    ald_hourly_data = pd.DataFrame.from_records(api.get_ald_hourly_data(params={'device_number': device_number}))
    if len(ald_hourly_data) == 0:
        ald_hourly_data = pd.DataFrame(columns=['timestamp', 'value', 'valid'])

    # Drop unused columns
    hourly_data.drop(hourly_data.columns.difference(['timestamp', 'dispersed_value', 'valid']), axis=1, inplace=True)
    maxflow_data.drop(maxflow_data.columns.difference(['timestamp', 'max_flow_value', 'max_flow_valid']), axis=1, inplace=True)
    pressure_data.drop(pressure_data.columns.difference(['reading_time', 'value', 'valid']), axis=1, inplace=True)
    ald_hourly_data.drop(ald_hourly_data.columns.difference(['timestamp', 'value', 'valid']), axis=1, inplace=True)

    # Filter out invalid records
    hourly_data = hourly_data[hourly_data['valid'] == True]
    maxflow_data = maxflow_data[maxflow_data['max_flow_valid'] == True]
    pressure_data = pressure_data[pressure_data['valid'] == True]
    ald_hourly_data = ald_hourly_data[ald_hourly_data['valid'] == True]

    # Format data
    hourly_data['timestamp'] = add_tzinfo(pd.to_datetime(hourly_data['timestamp']))
    maxflow_data['timestamp'] = add_tzinfo(pd.to_datetime(maxflow_data['timestamp']))
    maxflow_data['max_flow_value'] = maxflow_data['max_flow_value'].astype('int64')
    pressure_data['reading_time'] = add_tzinfo(pd.to_datetime(pressure_data['reading_time']))
    ald_hourly_data['timestamp'] = add_tzinfo(pd.to_datetime(ald_hourly_data['timestamp']))

    # Compute consumption period parameters
    start_time = hourly_data['timestamp'].min()
    end_time = hourly_data['timestamp'].max()
    origin = start_time.to_period('w').start_time.tz_localize('UTC')
    num_rows = 24 * pd.Timedelta('1w').days
    num_cols = (end_time.to_period('w').start_time.tz_localize('UTC') - origin + pd.Timedelta('1w')) // pd.Timedelta('1w')

    # Prepare alarm flags
    ticks = pd.date_range(start_time, end_time, freq='1h')
    k_full = num_cols * (24 * ticks.dayofweek + ticks.hour) + (ticks - origin) // pd.Timedelta('1w')
    timestamps = ticks.values
    alarm_data = { t: np.full(num_rows * num_cols, 0) for t in ALARM_TYPES }
    groups = alarms.groupby('alarm_type')
    for t, g in groups:
        alarm_status_matrix = [(timestamps >= start_time) & (timestamps < end_time) for start_time, end_time in zip(g['start_time'].values, g['end_time'].values)]
        alarm_ids = np.piecewise(np.zeros(len(timestamps)), alarm_status_matrix, g['id'].values)
        alarm_data[t].put(k_full, alarm_ids)
    alarm_data = { t: alarm_data[t].tolist() for t in ALARM_TYPES }

    maxflow_data = maxflow_data[maxflow_data['timestamp'].between(start_time, end_time)]
    pressure_data = pressure_data[pressure_data['reading_time'].between(start_time, end_time)]
    ald_hourly_data = ald_hourly_data[ald_hourly_data['timestamp'].between(start_time, end_time)]

    # Add row/column indices
    hourly_data['row'] = 24 * hourly_data['timestamp'].dt.dayofweek + hourly_data['timestamp'].dt.hour
    hourly_data['col'] = (hourly_data['timestamp'] - origin) // pd.Timedelta('1w')
    maxflow_data['row'] = 24 * maxflow_data['timestamp'].dt.dayofweek + maxflow_data['timestamp'].dt.hour
    maxflow_data['col'] = (maxflow_data['timestamp'] - origin) // pd.Timedelta('1w') if len(maxflow_data) > 0 else []
    pressure_data['row'] = 24 * pressure_data['reading_time'].dt.dayofweek + pressure_data['reading_time'].dt.hour
    pressure_data['col'] = (pressure_data['reading_time'] - origin) // pd.Timedelta('1w') if len(pressure_data) > 0 else []
    ald_hourly_data['row'] = 24 * ald_hourly_data['timestamp'].dt.dayofweek + ald_hourly_data['timestamp'].dt.hour
    ald_hourly_data['col'] = (ald_hourly_data['timestamp'] - origin) // pd.Timedelta('1w') if len(ald_hourly_data) > 0 else []

    # k is the index of the corresponding pixel in the flattened image
    hourly_data['k'] = num_cols * hourly_data.row + hourly_data.col
    maxflow_data['k'] = (num_cols * maxflow_data.row + maxflow_data.col).astype('int64')
    pressure_data['k'] = (num_cols * pressure_data.row + pressure_data.col).astype('int64')
    ald_hourly_data['k'] = (num_cols * ald_hourly_data.row + ald_hourly_data.col).astype('int64')

    # Drop columns that are no longer useful
    hourly_data.drop(hourly_data.columns.difference(['dispersed_value', 'k']), axis=1, inplace=True)
    maxflow_data.drop(maxflow_data.columns.difference(['max_flow_value', 'k']), axis=1, inplace=True)
    pressure_data.drop(pressure_data.columns.difference(['value', 'k']), axis=1, inplace=True)
    ald_hourly_data.drop(ald_hourly_data.columns.difference(['value', 'k']), axis=1, inplace=True)

    def get_low(values, min_output):
        if len(values) ==  0:
            return min_output
        # Use the 5th percentile as lower bound to exclude outliers
        low = np.nanpercentile(values, 5)
        if np.isnan(low):
            low = None
        return low

    def get_high(values, min_output):
        if len(values) ==  0:
            return min_output
        # Use the 95th percentile as upper bound to exclude outliers
        high = np.nanpercentile(values, 95)
        if high == 0:
            # Revert to the max in extreme cases
            high = max(values.max(), min_output)
        if np.isnan(high):
            high = None
        return high

    low = 0
    low_maxflow = 0
    low_pressure = get_low(pressure_data['value'].values, 0.01)
    low_ald_hourly = 0
    high = int(get_high(hourly_data['dispersed_value'].values, 1))
    high_maxflow = int(get_high(maxflow_data['max_flow_value'].values, 1))
    high_pressure = get_high(pressure_data['value'].values, 0.01)
    high_ald_hourly = int(get_high(ald_hourly_data['value'].values, 1))

    # Prepare data arrays
    arr = np.full(num_rows * num_cols, None)
    arr_maxflow = np.full(num_rows * num_cols, None)
    arr_pressure = np.full(num_rows * num_cols, None)
    arr_ald_hourly = np.full(num_rows * num_cols, None)
    arr.put(hourly_data['k'].values, hourly_data['dispersed_value'].values)
    arr_maxflow.put(maxflow_data['k'].values, maxflow_data['max_flow_value'].values)
    arr_pressure.put(pressure_data['k'].values, pressure_data['value'].values)
    arr_ald_hourly.put(ald_hourly_data['k'].values, ald_hourly_data['value'].values)

    # Compress response
    return func.HttpResponse(brotli.compress(json.dumps({
        'origin': origin.isoformat(),
        'low': low,
        'low_maxflow': low_maxflow,
        'low_pressure': low_pressure,
        'low_ald': low_ald_hourly,
        'high': high,
        'high_maxflow': high_maxflow,
        'high_pressure': high_pressure,
        'high_ald': high_ald_hourly,
        'data': [None if pd.isna(x) else x for x in arr.tolist()],
        'data_maxflow': [None if pd.isna(x) else x for x in arr_maxflow.tolist()],
        'data_pressure': [None if pd.isna(x) else x for x in arr_pressure.tolist()],
        'data_ald': [None if pd.isna(x) else x for x in arr_ald_hourly.tolist()],
        'alarm_data': alarm_data,
        'alarm_ids': alarms['id'].values.tolist(),
        'alarm_types': alarms['alarm_type'].values.tolist(),
        'alarm_start_times': alarms['start_time'].map(lambda x: x.isoformat()).tolist(),
        'alarm_end_times': alarms['end_time'].map(lambda x: x.isoformat() if x != TIMESTAMP_MAX_UTC else None).tolist()
    }).encode('utf-8')), headers={
        'Content-Encoding': 'br',
        'Content-Type': 'application/json'
    })
